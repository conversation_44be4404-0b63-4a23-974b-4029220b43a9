"use client"

import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useTranslations, useLocale } from 'next-intl'
import DatePickerTimeline from "../../components/date-picker-timeline"
import LanguageSwitcher from "@/components/LanguageSwitcher"
import { useLanguagePreference } from "@/hooks/useLanguagePreference"

export default function Home() {
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations();
  const [date, setDate] = useState<Date | undefined>(undefined)

  // Initialize language preference handling
  useLanguagePreference();
  // 弹窗状态
  const [showContactModal, setShowContactModal] = useState(false)
  const [showFollowModal, setShowFollowModal] = useState(false)

  useEffect(() => {
    setDate(new Date())
  }, [])

  // 模拟烟花秀事件数据
  const fireworksEvents = [
    {
      id: 1,
      titleKey: "events.sydney.title",
      locationKey: "events.sydney.location",
      date: "2024-12-31",
      time: "21:00",
      themeKey: "events.sydney.theme",
      image: "https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=300&fit=crop"
    },
    {
      id: 2,
      titleKey: "events.dubai.title",
      locationKey: "events.dubai.location",
      date: "2024-12-31",
      time: "00:00",
      themeKey: "events.dubai.theme",
      image: "https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=400&h=300&fit=crop"
    },
    {
      id: 3,
      titleKey: "events.london.title",
      locationKey: "events.london.location",
      date: "2024-12-31",
      time: "00:00",
      themeKey: "events.london.theme",
      image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop"
    }
  ]

  // 模拟旅行攻略数据
  const travelGuides = [
    {
      id: 1,
      titleKey: "guides.sydney.title",
      descriptionKey: "guides.sydney.description",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
      readTimeKey: "guides.sydney.readTime"
    },
    {
      id: 2,
      titleKey: "guides.dubai.title",
      descriptionKey: "guides.dubai.description",
      image: "https://images.unsplash.com/photo-1518684079-3c830dcef090?w=400&h=300&fit=crop",
      readTimeKey: "guides.dubai.readTime"
    },
    {
      id: 3,
      titleKey: "guides.europe.title",
      descriptionKey: "guides.europe.description",
      image: "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=400&h=300&fit=crop",
      readTimeKey: "guides.europe.readTime"
    }
  ]

  // 模拟经典烟花秀档案
  const classicFireworks = [
    {
      id: 1,
      titleKey: "archives.beijing.title",
      year: "archives.beijing.year",
      descriptionKey: "archives.beijing.description",
      image: "https://images.unsplash.com/photo-1465447142348-e9952c393450?w=400&h=300&fit=crop",
      significanceKey: "archives.beijing.significance"
    },
    {
      id: 2,
      titleKey: "archives.london2000.title",
      year: "archives.london2000.year",
      descriptionKey: "archives.london2000.description",
      image: "https://images.unsplash.com/photo-1420406676079-b8491f2d07c8?w=400&h=300&fit=crop",
      significanceKey: "archives.london2000.significance"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* 导航栏 */}
      <nav className="bg-slate-900/50 backdrop-blur-md border-b border-slate-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">🎆</span>
              </div>
              <h1 className="text-2xl font-bold text-white">{t('navigation.title')}</h1>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex space-x-6 text-slate-300">
                <a href="#calendar" className="hover:text-orange-400 transition-colors">{t('navigation.calendar')}</a>
                <a href="#events" className="hover:text-orange-400 transition-colors">{t('navigation.events')}</a>
                <a href="#guides" className="hover:text-orange-400 transition-colors">{t('navigation.guides')}</a>
                <a href="#archives" className="hover:text-orange-400 transition-colors">{t('navigation.archives')}</a>
                <a href="#submit" className="hover:text-orange-400 transition-colors">{t('navigation.submit')}</a>
              </div>
              <LanguageSwitcher />
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">
        {/* 英雄区域 */}
        <section className="text-center py-12">
          <h2 className="text-5xl font-bold text-white mb-4">
            {t('hero.title')}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              {t('hero.titleHighlight')}
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            {t('hero.description')}
          </p>
        </section>

        {/* 日历板块 */}
        <section id="calendar" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">{t('sections.calendar.title')}</h3>
            <p className="text-slate-300">{t('sections.calendar.description')}</p>
          </div>
          <DatePickerTimeline />
        </section>

        {/* 烟花秀事件列表 */}
        <section id="events" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">{t('sections.events.title')}</h3>
            <p className="text-slate-300">{t('sections.events.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {fireworksEvents.map((event) => (
              <Card key={event.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 group">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={event.image}
                    alt={t(event.titleKey)}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-orange-500 hover:bg-orange-600">
                      {t(event.themeKey)}
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-white">{t(event.titleKey)}</CardTitle>
                  <CardDescription className="text-slate-300">
                    📍 {t(event.locationKey)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-slate-300">
                    <p>📅 {event.date}</p>
                    <p>🕘 {event.time}</p>
                  </div>
                  <Button
                    className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    onClick={() => router.push(`/${locale}/event-detail-${event.id}`)}
                  >
                    {t('buttons.viewDetails')}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 旅行攻略卡片 */}
        <section id="guides" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">{t('sections.guides.title')}</h3>
            <p className="text-slate-300">{t('sections.guides.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {travelGuides.map((guide) => (
              <Card key={guide.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 group">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={guide.image}
                    alt={t(guide.titleKey)}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary">
                      📚 {t(guide.readTimeKey)}
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-white group-hover:text-orange-400 transition-colors">
                    {t(guide.titleKey)}
                  </CardTitle>
                  <CardDescription className="text-slate-300">
                    {t(guide.descriptionKey)}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    onClick={() => router.push(`/${locale}/travel-guide-${guide.id}`)}
                  >
                    {t('buttons.readGuide')}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 经典烟花秀档案 */}
        <section id="archives" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">{t('sections.archives.title')}</h3>
            <p className="text-slate-300">{t('sections.archives.description')}</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {classicFireworks.map((archive) => (
              <Card key={archive.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <img
                      src={archive.image}
                      alt={t(archive.titleKey)}
                      className="w-full h-64 md:h-full object-cover rounded-t-lg md:rounded-l-lg md:rounded-t-none"
                    />
                  </div>
                  <div className="md:w-1/2 p-6 flex flex-col justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className="bg-amber-500 hover:bg-amber-600">
                          {t(archive.year)}
                        </Badge>
                        <Badge variant="outline" className="border-slate-600 text-slate-300">
                          {t(archive.significanceKey)}
                        </Badge>
                      </div>
                      <CardTitle className="text-white mb-3">{t(archive.titleKey)}</CardTitle>
                      <CardDescription className="text-slate-300 mb-4">
                        {t(archive.descriptionKey)}
                      </CardDescription>
                    </div>
                    <Button
                      className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                      onClick={() => router.push(`/${locale}/show-profile-${archive.id}`)}
                    >
                      {t('buttons.learnMore')}
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </section>

        {/* 投稿区 */}
        <section id="submit" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">{t('sections.submit.title')}</h3>
            <p className="text-slate-300">{t('sections.submit.description')}</p>
          </div>
          <Card className="max-w-2xl mx-auto bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">{t('form.title')}</CardTitle>
              <CardDescription className="text-slate-300">
                {t('form.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">{t('form.eventName')}</label>
                <Input
                  placeholder={t('form.eventNamePlaceholder')}
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">{t('form.location')}</label>
                <Input
                  placeholder={t('form.locationPlaceholder')}
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">{t('form.experience')}</label>
                <textarea
                  placeholder={t('form.experiencePlaceholder')}
                  className="w-full p-3 rounded-md bg-slate-700 border border-slate-600 text-white placeholder:text-slate-400 min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">{t('form.uploadPhotos')}</label>
                <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
                  <p className="text-slate-400">{t('form.uploadText')}</p>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                {t('form.submit')}
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>

      {/* 页脚 */}
      <footer className="bg-slate-900 border-t border-slate-700 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">🎆</span>
                </div>
                <h3 className="text-xl font-bold text-white">{t('navigation.title')}</h3>
              </div>
              <p className="text-slate-300 mb-4">
                {t('footer.description')}
              </p>
              <div className="flex space-x-4">
                <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" size="sm" onClick={() => setShowFollowModal(true)}>
                  {t('footer.followUs')}
                </Button>
                <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" size="sm" onClick={() => setShowContactModal(true)}>
                  {t('footer.contact')}
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.quickLinks')}</h4>
              <ul className="space-y-2 text-slate-300">
                <li><a href="#calendar" className="hover:text-orange-400 transition-colors">{t('navigation.calendar')}</a></li>
                <li><a href="#events" className="hover:text-orange-400 transition-colors">{t('navigation.events')}</a></li>
                <li><a href="#guides" className="hover:text-orange-400 transition-colors">{t('navigation.guides')}</a></li>
                <li><a href="#archives" className="hover:text-orange-400 transition-colors">{t('navigation.archives')}</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">{t('footer.contactInfo')}</h4>
              <ul className="space-y-2 text-slate-300">
                <li>📧 {t('footer.email')}</li>
                <li>📱 {t('footer.wechat')}</li>
                <li>🌐 {t('footer.website')}</li>
                <li>📍 {t('footer.service')}</li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-slate-700" />

          <div className="flex flex-col md:flex-row justify-between items-center text-slate-400 text-sm">
            <p>{t('footer.copyright')}</p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <a href="#" className="hover:text-orange-400 transition-colors">{t('footer.privacy')}</a>
              <a href="#" className="hover:text-orange-400 transition-colors">{t('footer.terms')}</a>
              <a href="#" className="hover:text-orange-400 transition-colors">{t('footer.sitemap')}</a>
            </div>
          </div>
        </div>
      </footer>

      {/* 弹窗组件 */}
      {(showContactModal || showFollowModal) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
          <div className="bg-slate-900 rounded-2xl shadow-2xl p-8 max-w-xs w-full text-center border border-orange-400">
            <h2 className="text-2xl font-bold text-gradient-goldorange mb-4">{t('modal.contactTitle')}</h2>
            <p className="text-lg text-white mb-6 select-all">{t('modal.contactEmail')}</p>
            <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" onClick={() => { setShowContactModal(false); setShowFollowModal(false); }}>
              {t('buttons.close')}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
