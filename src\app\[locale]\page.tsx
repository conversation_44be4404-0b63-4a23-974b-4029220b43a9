import { useTranslations, useLocale } from 'next-intl'

export default function Home() {
  const locale = useLocale();
  const t = useTranslations();

  return (
    <div style={{ padding: '20px', backgroundColor: '#1a1a1a', color: 'white', minHeight: '100vh' }}>
      <h1>🎆 {t('navigation.title')}</h1>
      <p>Current locale: {locale}</p>
      <p>Hero title: {t('hero.title')}</p>
      <p>Hero highlight: {t('hero.titleHighlight')}</p>
      <p>Description: {t('hero.description')}</p>

      <div style={{ marginTop: '20px' }}>
        <h2>Navigation Test:</h2>
        <ul>
          <li>Calendar: {t('navigation.calendar')}</li>
          <li>Events: {t('navigation.events')}</li>
          <li>Guides: {t('navigation.guides')}</li>
          <li>Archives: {t('navigation.archives')}</li>
          <li>Submit: {t('navigation.submit')}</li>
        </ul>
      </div>
    </div>
  );
}
