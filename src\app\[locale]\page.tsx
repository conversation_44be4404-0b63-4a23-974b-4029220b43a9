"use client"

import { useTranslations, useLocale } from 'next-intl'
import LanguageSwitcher from "@/components/LanguageSwitcher"

export default function Home() {
  const locale = useLocale();
  const t = useTranslations();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* 导航栏 */}
      <nav className="bg-slate-900/50 backdrop-blur-md border-b border-slate-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">🎆</span>
              </div>
              <h1 className="text-2xl font-bold text-white">{t('navigation.title')}</h1>
            </div>
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex space-x-6 text-slate-300">
                <a href="#calendar" className="hover:text-orange-400 transition-colors">{t('navigation.calendar')}</a>
                <a href="#events" className="hover:text-orange-400 transition-colors">{t('navigation.events')}</a>
                <a href="#guides" className="hover:text-orange-400 transition-colors">{t('navigation.guides')}</a>
                <a href="#archives" className="hover:text-orange-400 transition-colors">{t('navigation.archives')}</a>
                <a href="#submit" className="hover:text-orange-400 transition-colors">{t('navigation.submit')}</a>
              </div>
              <LanguageSwitcher />
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">
        {/* 英雄区域 */}
        <section className="text-center py-12">
          <h2 className="text-5xl font-bold text-white mb-4">
            {t('hero.title')}
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              {t('hero.titleHighlight')}
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            {t('hero.description')}
          </p>
        </section>

        <div className="text-center">
          <p className="text-white">Current locale: {locale}</p>
          <p className="text-white">Test translation: {t('navigation.title')}</p>
        </div>
      </div>
    </div>
  );
}
