"use client"

import type * as React from "react"
import Image from "next/image" // Import Image component
import { format } from "date-fns"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface DateSpecificCardsProps {
  date: Date
  textClassName?: string
}

interface SpecificCard {
  date: Date
  content: string
  imageUrl: string // Added imageUrl property
}

// Improved card data structure for easier future additions
const specificCardsData: SpecificCard[] = [
  {
    date: new Date(2025, 6, 15), // Month is 0-indexed (July is 6)
    content: "Card Content 1 for 2025-07-15",
    imageUrl: "/placeholder.svg?height=150&width=300", // Placeholder image
  },
  {
    date: new Date(2025, 7, 4), // August is 7
    content: "Card Content 2 for 2025-08-04",
    imageUrl: "/placeholder.svg?height=150&width=300", // Placeholder image
  },
  // Add more cards here following the same structure:
  // {
  //   date: new Date(YYYY, MM - 1, DD), // Remember month is 0-indexed
  //   content: "Your custom content here",
  //   imageUrl: "/path/to/your/image.png",
  // },
  {
    date: new Date(2025, 6, 18),
    content: "镰仓烟花大会 (Kamakura Fireworks Festival)\n地点：日本 镰仓 ([JapanTravel](https://www.japan.travel/))",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 6, 19),
    content: "温哥华 Honda Celebration of Light – 场次1\n地点：加拿大 温哥华",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 6, 21),
    content: "伊势神宫烟花 & 名古屋港烟火节\n地点：日本 三重 / 名古屋",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 6, 23),
    content: "温哥华 Celebration of Light – 场次2\n地点：加拿大 温哥华",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 7月24–25日
  {
    date: new Date(2025, 6, 24),
    content: "十日市、天神祭、札幌花火等多场日本烟火大会\n地点：日本 大阪、北海道等",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 6, 25),
    content: "十日市、天神祭、札幌花火等多场日本烟火大会\n地点：日本 大阪、北海道等",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 7月25–26日
  {
    date: new Date(2025, 6, 25),
    content: "镰仓、热海、东京、长野等地多场烟花大会\n地点：日本 多地",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 6, 26),
    content: "镰仓、热海、东京、长野等地多场烟花大会\n地点：日本 多地",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 7月26日
  {
    date: new Date(2025, 6, 26),
    content: "温哥华 Celebration of Light – 场次3 + 隅田川烟花大会\n地点：加拿大温哥华 & 日本东京",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 8月2–3日
  {
    date: new Date(2025, 7, 2),
    content: "长冈烟花大会 (Nagaoka Fireworks Festival)\n地点：日本 新潟",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 7, 3),
    content: "长冈烟花大会 (Nagaoka Fireworks Festival)\n地点：日本 新潟",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 8月6–8日
  {
    date: new Date(2025, 7, 6),
    content: "湖北＆滋贺多个湖边及市区大型烟火\n地点：日本 滋贺等",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 7, 7),
    content: "湖北＆滋贺多个湖边及市区大型烟火\n地点：日本 滋贺等",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  {
    date: new Date(2025, 7, 8),
    content: "湖北＆滋贺多个湖边及市区大型烟火\n地点：日本 滋贺等",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 8月30日
  {
    date: new Date(2025, 7, 30),
    content: "大曲烟火比赛 (Omagari Fireworks Festival)\n地点：日本 秋田",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
  // 9月13日
  {
    date: new Date(2025, 8, 13),
    content: "Katakai 大尺玉烟火节\n地点：日本 新潟 Katakai",
    imageUrl: "/placeholder.svg?height=150&width=300",
  },
]

const DateSpecificCards: React.FC<DateSpecificCardsProps> = ({ date, textClassName = "" }) => {
  const formattedCurrentDate = format(date, "yyyy-MM-dd")

  const cardsToDisplay = specificCardsData.filter((card) => format(card.date, "yyyy-MM-dd") === formattedCurrentDate)

  return (
    <div className="p-4 min-w-[300px]">
      {cardsToDisplay.length > 0 ? (
        cardsToDisplay.map((card, index) => (
          <Card key={index} className="mb-4 overflow-hidden">
            <div className="relative w-full h-[150px]">
              <Image
                src={card.imageUrl || "/placeholder.svg"}
                alt={`Image for ${formattedCurrentDate}`}
                layout="fill"
                objectFit="cover"
                className="rounded-t-lg"
              />
            </div>
            <CardHeader>
              <CardTitle className={textClassName}>Information for {formattedCurrentDate}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className={textClassName}>{card.content}</p>
            </CardContent>
          </Card>
        ))
      ) : (
        <p className={"text-center " + textClassName}>No specific cards for {formattedCurrentDate}.</p>
      )}
    </div>
  )
}

export default DateSpecificCards 