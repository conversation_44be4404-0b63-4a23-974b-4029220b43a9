import * as React from "react"

interface PopoverProps {
  children: React.ReactNode
}

export function Popover({ children }: PopoverProps) {
  return <div className="relative">{children}</div>
}

interface PopoverTriggerProps {
  asChild?: boolean
  children: React.ReactNode
}

export function PopoverTrigger({ children }: PopoverTriggerProps) {
  return <div>{children}</div>
}

interface PopoverContentProps {
  className?: string
  children: React.ReactNode
}

export function PopoverContent({ className, children }: PopoverContentProps) {
  return <div className={`absolute z-50 bg-transparent shadow-none border-none rounded-none p-4 mt-2 ${className || ""}`}>{children}</div>
} 