"use client"

import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import DatePickerTimeline from "../components/date-picker-timeline"

export default function Home() {
  const router = useRouter();
  const [date, setDate] = useState<Date | undefined>(undefined)
  // 弹窗状态
  const [showContactModal, setShowContactModal] = useState(false)
  const [showFollowModal, setShowFollowModal] = useState(false)

  useEffect(() => {
    setDate(new Date())
  }, [])

  // 模拟烟花秀事件数据
  const fireworksEvents = [
    {
      id: 1,
      title: "悉尼新年烟花秀",
      location: "悉尼海港大桥",
      date: "2024-12-31",
      time: "21:00",
      theme: "海港之光",
      image: "https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=400&h=300&fit=crop"
    },
    {
      id: 2,
      title: "迪拜新年庆典",
      location: "哈利法塔",
      date: "2024-12-31",
      time: "00:00",
      theme: "未来之城",
      image: "https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=400&h=300&fit=crop"
    },
    {
      id: 3,
      title: "伦敦泰晤士河烟花节",
      location: "伦敦眼",
      date: "2024-12-31",
      time: "00:00",
      theme: "皇家庆典",
      image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=300&fit=crop"
    }
  ]

  // 模拟旅行攻略数据
  const travelGuides = [
    {
      id: 1,
      title: "如何在悉尼观看最佳烟花秀",
      description: "详细介绍悉尼最佳观赏点位和预订攻略",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
      readTime: "5分钟"
    },
    {
      id: 2,
      title: "迪拜烟花节完整观光指南",
      description: "从住宿到交通的全方位攻略",
      image: "https://images.unsplash.com/photo-1518684079-3c830dcef090?w=400&h=300&fit=crop",
      readTime: "8分钟"
    },
    {
      id: 3,
      title: "欧洲新年烟花之旅规划",
      description: "畅游多个城市，体验不同文化的烟花庆典",
      image: "https://images.unsplash.com/photo-1467269204594-9661b134dd2b?w=400&h=300&fit=crop",
      readTime: "12分钟"
    }
  ]

  // 模拟经典烟花秀档案
  const classicFireworks = [
    {
      id: 1,
      title: "北京奥运会开幕式烟花",
      year: "2008",
      description: "史上最壮观的奥运开幕式烟花表演",
      image: "https://images.unsplash.com/photo-1465447142348-e9952c393450?w=400&h=300&fit=crop",
      significance: "奥运历史经典"
    },
    {
      id: 2,
      title: "千禧年伦敦烟花秀",
      year: "2000",
      description: "庆祝新千年的标志性烟花表演",
      image: "https://images.unsplash.com/photo-1420406676079-b8491f2d07c8?w=400&h=300&fit=crop",
      significance: "千年庆典"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* 导航栏 */}
      <nav className="bg-slate-900/50 backdrop-blur-md border-b border-slate-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">🎆</span>
              </div>
              <h1 className="text-2xl font-bold text-white">全球烟花秀日历</h1>
            </div>
            <div className="hidden md:flex space-x-6 text-slate-300">
              <a href="#calendar" className="hover:text-orange-400 transition-colors">日历</a>
              <a href="#events" className="hover:text-orange-400 transition-colors">活动</a>
              <a href="#guides" className="hover:text-orange-400 transition-colors">攻略</a>
              <a href="#archives" className="hover:text-orange-400 transition-colors">档案</a>
              <a href="#submit" className="hover:text-orange-400 transition-colors">投稿</a>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-12">
        {/* 英雄区域 */}
        <section className="text-center py-12">
          <h2 className="text-5xl font-bold text-white mb-4">
            探索世界最精彩的
            <span className="bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              烟花盛典
            </span>
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            发现全球最壮观的烟花表演，规划您的完美观赏之旅，记录每一个绚烂瞬间
          </p>
        </section>

        {/* 日历板块 */}
        <section id="calendar" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">烟花秀日历</h3>
            <p className="text-slate-300">查看即将到来的全球烟花活动</p>
          </div>
          <DatePickerTimeline />
        </section>

        {/* 烟花秀事件列表 */}
        <section id="events" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">落幕的烟花秀</h3>
            <p className="text-slate-300">不容错过的精彩烟花活动</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {fireworksEvents.map((event) => (
              <Card key={event.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 group">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={event.image}
                    alt={event.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-orange-500 hover:bg-orange-600">
                      {event.theme}
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-white">{event.title}</CardTitle>
                  <CardDescription className="text-slate-300">
                    📍 {event.location}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-slate-300">
                    <p>📅 {event.date}</p>
                    <p>🕘 {event.time}</p>
                  </div>
                  <Button
                    className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    onClick={() => router.push(`/event-detail-${event.id}`)}
                  >
                    查看详情
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 旅行攻略卡片 */}
        <section id="guides" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">旅行攻略</h3>
            <p className="text-slate-300">专业的观赏指南，让您的烟花之旅更完美</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {travelGuides.map((guide) => (
              <Card key={guide.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300 group">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={guide.image}
                    alt={guide.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary">
                      📚 {guide.readTime}
                    </Badge>
                  </div>
                </div>
                <CardHeader>
                  <CardTitle className="text-white group-hover:text-orange-400 transition-colors">
                    {guide.title}
                  </CardTitle>
                  <CardDescription className="text-slate-300">
                    {guide.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                    onClick={() => router.push(`/travel-guide-${guide.id}`)}
                  >
                    阅读攻略
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 经典烟花秀档案 */}
        <section id="archives" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">经典烟花秀档案</h3>
            <p className="text-slate-300">回顾历史上最令人难忘的烟花表演</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {classicFireworks.map((archive) => (
              <Card key={archive.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
                <div className="md:flex">
                  <div className="md:w-1/2">
                    <img
                      src={archive.image}
                      alt={archive.title}
                      className="w-full h-64 md:h-full object-cover rounded-t-lg md:rounded-l-lg md:rounded-t-none"
                    />
                  </div>
                  <div className="md:w-1/2 p-6 flex flex-col justify-between">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className="bg-amber-500 hover:bg-amber-600">
                          {archive.year}
                        </Badge>
                        <Badge variant="outline" className="border-slate-600 text-slate-300">
                          {archive.significance}
                        </Badge>
                      </div>
                      <CardTitle className="text-white mb-3">{archive.title}</CardTitle>
                      <CardDescription className="text-slate-300 mb-4">
                        {archive.description}
                      </CardDescription>
                    </div>
                    <Button
                      className="w-full mt-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                      onClick={() => router.push(`/show-profile-${archive.id}`)}
                    >
                      了解更多
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </section>

        {/* 投稿区 */}
        <section id="submit" className="space-y-6">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-2">分享您的烟花体验</h3>
            <p className="text-slate-300">投稿您拍摄的烟花照片和观赏心得</p>
          </div>
          <Card className="max-w-2xl mx-auto bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">投稿表单</CardTitle>
              <CardDescription className="text-slate-300">
                分享您的烟花秀照片和体验，让更多人发现美好
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">活动名称</label>
                <Input
                  placeholder="请输入烟花秀名称"
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">地点</label>
                <Input
                  placeholder="请输入举办地点"
                  className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">您的观赏体验</label>
                <textarea
                  placeholder="分享您的观赏心得和建议..."
                  className="w-full p-3 rounded-md bg-slate-700 border border-slate-600 text-white placeholder:text-slate-400 min-h-[100px]"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">上传照片</label>
                <div className="border-2 border-dashed border-slate-600 rounded-lg p-6 text-center">
                  <p className="text-slate-400">点击或拖拽上传照片</p>
                </div>
              </div>
              <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                提交投稿
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>

      {/* 页脚 */}
      <footer className="bg-slate-900 border-t border-slate-700 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-red-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">🎆</span>
                </div>
                <h3 className="text-xl font-bold text-white">全球烟花秀日历</h3>
              </div>
              <p className="text-slate-300 mb-4">
                致力于为烟花爱好者提供最全面的全球烟花活动信息，
                帮助您发现和规划完美的烟花观赏之旅。
              </p>
              <div className="flex space-x-4">
                <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" size="sm" onClick={() => setShowFollowModal(true)}>
                  关注我们
                </Button>
                <Button className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" size="sm" onClick={() => setShowContactModal(true)}>
                  联系合作
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">快速链接</h4>
              <ul className="space-y-2 text-slate-300">
                <li><a href="#calendar" className="hover:text-orange-400 transition-colors">烟花日历</a></li>
                <li><a href="#events" className="hover:text-orange-400 transition-colors">热门活动</a></li>
                <li><a href="#guides" className="hover:text-orange-400 transition-colors">旅行攻略</a></li>
                <li><a href="#archives" className="hover:text-orange-400 transition-colors">经典档案</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-white mb-4">联系信息</h4>
              <ul className="space-y-2 text-slate-300">
                <li>📧 <EMAIL></li>
                <li>📱 关注微信公众号</li>
                <li>🌐 官方网站</li>
                <li>📍 全球服务</li>
              </ul>
            </div>
          </div>

          <Separator className="my-8 bg-slate-700" />

          <div className="flex flex-col md:flex-row justify-between items-center text-slate-400 text-sm">
            <p>&copy; 2024 全球烟花秀日历. 保留所有权利.</p>
            <div className="flex space-x-4 mt-4 md:mt-0">
              <a href="#" className="hover:text-orange-400 transition-colors">隐私政策</a>
              <a href="#" className="hover:text-orange-400 transition-colors">服务条款</a>
              <a href="#" className="hover:text-orange-400 transition-colors">网站地图</a>
            </div>
          </div>
        </div>
      </footer>

      {/* 弹窗组件 */}
      {(showContactModal || showFollowModal) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
          <div className="bg-slate-900 rounded-2xl shadow-2xl p-8 max-w-xs w-full text-center border border-orange-400">
            <h2 className="text-2xl font-bold text-gradient-goldorange mb-4">联系我们</h2>
            <p className="text-lg text-white mb-6 select-all">邮箱：<EMAIL></p>
            <Button className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-none" onClick={() => { setShowContactModal(false); setShowFollowModal(false); }}>
              关闭
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
