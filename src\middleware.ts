import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './i18n';
import { NextRequest } from 'next/server';

const intlMiddleware = createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Always use locale prefix
  localePrefix: 'always'
});

export default function middleware(request: NextRequest) {
  // Check for stored language preference in cookies
  const preferredLanguage = request.cookies.get('preferred-language')?.value;

  // If user has a stored preference and is visiting root, redirect to preferred language
  if (preferredLanguage && locales.includes(preferredLanguage as any) && request.nextUrl.pathname === '/') {
    const url = request.nextUrl.clone();
    url.pathname = `/${preferredLanguage}`;
    return Response.redirect(url);
  }

  return intlMiddleware(request);
}

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(zh|en)/:path*']
};
