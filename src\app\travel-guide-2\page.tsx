import React from "react";
export default function TravelGuide2() {
  return (
    <div className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-4 text-gradient-goldorange">迪拜烟花节完整观光指南｜2025全球烟花日历</h1>
      <p className="mb-4 text-lg">从住宿到交通的全方位攻略，助你畅游迪拜新年烟花节。</p>
      <h2 className="text-2xl font-semibold mt-8 mb-2">🎆 1. 观赏点推荐</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>哈利法塔（Burj Khalifa）周边：最佳观赏地，需提前占位。</li>
        <li>迪拜河畔（Dubai Creek）：适合家庭，氛围热烈。</li>
        <li>朱美拉海滩（Jumeirah Beach）：可远眺烟花与城市天际线。</li>
      </ul>
      <h2 className="text-2xl font-semibold mt-8 mb-2">🏨 2. 住宿建议</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>市中心酒店：步行可达主会场，节省交通时间。</li>
        <li>海滨度假村：享受沙滩与烟花双重体验。</li>
      </ul>
      <h2 className="text-2xl font-semibold mt-8 mb-2">🚗 3. 交通与出行</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>地铁：新年夜地铁延长运营，建议提前购票。</li>
        <li>出租车/网约车：高峰期需预留充足时间。</li>
      </ul>
      <h2 className="text-2xl font-semibold mt-8 mb-2">🍽️ 4. 美食体验</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>阿拉伯风味餐厅：推荐尝试当地特色美食。</li>
        <li>国际自助餐厅：多元选择，适合家庭聚会。</li>
      </ul>
      <h2 className="text-2xl font-semibold mt-8 mb-2">🎉 5. 行前贴士</h2>
      <ul className="list-disc pl-6 mb-4">
        <li>提前预订门票和酒店，节省预算。</li>
        <li>关注官方公告，了解交通管制信息。</li>
        <li>携带轻便衣物，注意防晒。</li>
      </ul>
      <p className="mt-8 text-lg">祝你在迪拜度过一个难忘的新年烟花之夜！</p>
    </div>
  );
} 