"use client"

import type * as React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { CalendarIcon } from "lucide-react"
import { format, getDaysInMonth, setDate, setMonth, setYear } from "date-fns"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import DateSpecificCards from "./date-specific-cards" // Import the new card display component

// Helper to get a fixed range of years
const getYears = () => {
  const years = []
  for (let i = 1980; i <= 2040; i++) {
    years.push(i)
  }
  return years
}

// Helper to get abbreviated month names
const getMonths = () => {
  return Array.from({ length: 12 }, (_, i) => format(new Date(2000, i, 1), "MMM"))
}

// Helper to get days in a specific month and year
const getDays = (year: number, month: number) => {
  const daysInMonth = getDaysInMonth(new Date(year, month))
  return Array.from({ length: daysInMonth }, (_, i) => i + 1)
}

interface TimelineItem {
  value: string | number // The actual value to be used for date manipulation (e.g., year number, month index)
  display: string | number // The value to be displayed in the timeline (e.g., "Jan", 2023)
  uniqueKey: string // A truly unique key for React's list rendering and internal ref mapping
}

interface TimelineProps {
  items: TimelineItem[]
  selectedValueKey: string // The uniqueKey of the currently selected item
  onValueChange: (item: TimelineItem) => void
  itemWidth: number // Width of each item in pixels for consistent layout
  className?: string
}

const Timeline: React.FC<TimelineProps> = ({ items = [], selectedValueKey, onValueChange, itemWidth, className }) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<Map<string, HTMLDivElement>>(new Map())
  const [isHovered, setIsHovered] = useState(false)
  const isDragging = useRef(false)
  const dragStartX = useRef(0)
  const scrollStartLeft = useRef(0)

  // 修复页面滚动 bug: 添加 passive: false 的原生事件监听器
  useEffect(() => {
    const scrollDiv = scrollRef.current
    if (!scrollDiv) return

    const handleWheel = (e: WheelEvent) => {
      if (isHovered) {
        e.preventDefault()
      }
    }

    scrollDiv.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      scrollDiv.removeEventListener('wheel', handleWheel)
    }
  }, [isHovered])

  // 无限滚动：渲染三组
  const repeatedItems = [...items, ...items, ...items]
  const middleGroupStart = items.length

  // 初始定位到中间组
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollLeft = items.length * itemWidth
    }
  }, [items.length, itemWidth])

  // 滚动到边界时跳回中间组
  const handleInfiniteScroll = () => {
    if (!scrollRef.current) return
    const maxScroll = itemWidth * items.length * 2
    if (scrollRef.current.scrollLeft < itemWidth * 0.5) {
      // 到头部，跳到中间组
      scrollRef.current.scrollLeft += items.length * itemWidth
    } else if (scrollRef.current.scrollLeft > maxScroll - itemWidth * 0.5) {
      // 到尾部，跳到中间组
      scrollRef.current.scrollLeft -= items.length * itemWidth
    }
  }

  // 居中选中项
  useEffect(() => {
    if (!scrollRef.current) return
    // 找到中间组中选中项的索引
    const selectedIndex = items.findIndex((item) => item.uniqueKey === selectedValueKey)
    if (selectedIndex === -1) return
    // 中间组的选中项在 repeatedItems 的索引
    const centerIndex = middleGroupStart + selectedIndex
    const containerWidth = scrollRef.current.clientWidth
    const scrollLeft = centerIndex * itemWidth - containerWidth / 2 + itemWidth / 2
    scrollRef.current.scrollTo({ left: scrollLeft, behavior: "smooth" })
  }, [selectedValueKey, items, itemWidth])

  // 滚轮和拖拽时都要做无限跳转
  const handleScroll = useCallback(() => {
    handleInfiniteScroll()
  }, [items.length, itemWidth])

  const debouncedHandleScroll = useCallback(
    debounce(handleScroll, 100),
    [handleScroll],
  )

  // 滚轮切换选中项
  const handleWheelScroll = useCallback(
    (event: React.WheelEvent<HTMLDivElement>) => {
      // event.preventDefault() 从这里移除，交给原生事件监听器处理
      const selectedIndex = items.findIndex((item) => item.uniqueKey === selectedValueKey)
      if (selectedIndex === -1) return
      const direction = event.deltaY > 0 ? 1 : -1
      let newIndex = selectedIndex + direction
      if (newIndex < 0) newIndex = items.length - 1
      if (newIndex >= items.length) newIndex = 0
      const newItem = items[newIndex]
      if (newItem && newItem.uniqueKey !== selectedValueKey) {
        onValueChange(newItem)
      }
    },
    [items, selectedValueKey, onValueChange],
  )

  // 拖拽滑动逻辑（不变）
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.button !== 0) return
    isDragging.current = true
    dragStartX.current = e.clientX
    scrollStartLeft.current = scrollRef.current ? scrollRef.current.scrollLeft : 0
    document.body.style.cursor = 'grabbing'
  }
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.current || !scrollRef.current) return
    const dx = e.clientX - dragStartX.current
    scrollRef.current.scrollLeft = scrollStartLeft.current - dx
    handleInfiniteScroll()
  }
  const handleMouseUp = () => {
    isDragging.current = false
    document.body.style.cursor = ''
  }
  useEffect(() => {
    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('mouseup', handleMouseUp)
    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('mouseup', handleMouseUp)
    }
  }, [])

  return (
    <div
      ref={scrollRef}
      onScroll={debouncedHandleScroll}
      onWheel={handleWheelScroll}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={handleMouseDown}
      style={{ cursor: isDragging.current ? 'grabbing' : 'grab' }}
      className={cn(
        "flex overflow-x-scroll no-scrollbar py-2 scroll-smooth",
        className,
      )}
    >
      {repeatedItems.map((item, idx) => (
        <div
          key={idx + '-' + item.uniqueKey}
          ref={(el) => {
            if (el) itemRefs.current.set(item.uniqueKey, el)
          }}
          className={cn(
            "flex-shrink-0 flex items-center justify-center text-lg font-medium transition-colors duration-200",
            "w-[--item-width]",
            selectedValueKey === item.uniqueKey ? "text-gradient-goldorange" : "text-muted-foreground",
          )}
          style={{ "--item-width": `${itemWidth}px` } as React.CSSProperties}
        >
          {item.display}
        </div>
      ))}
    </div>
  )
}

// Generic debounce utility function
function debounce<T extends (...args: any[]) => void>(func: T, delay: number) {
  let timeout: NodeJS.Timeout
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), delay)
  } as T
}

export default function DatePickerTimeline() {
  const [date, setDateState] = useState<Date>(new Date())
  const [collapsed, setCollapsed] = useState(true)

  const currentYear = date.getFullYear()
  const currentMonthIndex = date.getMonth()
  const currentDay = date.getDate()

  // 准备 items for Timelines
  const years = getYears().map((y) => ({ value: y, display: y, uniqueKey: `year-${y}` }))

  // 统一月份逻辑
  const months = getMonths().map((monthName, monthIndex) => ({
    value: monthIndex,
    display: monthName,
    uniqueKey: `month-${monthIndex}`,
  }))

  const days = getDays(currentYear, currentMonthIndex).map((d) => ({
    value: d,
    display: d,
    uniqueKey: `day-${d}`,
  }))

  // Handlers for timeline value changes
  const handleYearChange = useCallback((item: TimelineItem) => {
    setDateState((prevDate) => setYear(prevDate, item.value as number))
  }, [])

  const handleMonthChange = useCallback((item: TimelineItem) => {
    setDateState((prevDate) => setMonth(prevDate, item.value as number))
  }, [])

  const handleDayChange = useCallback((item: TimelineItem) => {
    setDateState((prevDate) => setDate(prevDate, item.value as number))
  }, [])

  const itemWidth = 80 // Define a fixed width for each timeline item for consistent centering

  // Determine the uniqueKey for the currently selected month in the middle set
  // This ensures that when the month changes, the timeline scrolls to the corresponding month in the middle set.
  const selectedMonthUniqueKey = `month-${currentMonthIndex}`

  return (
    <div className="flex flex-col items-center justify-center p-4 w-full mx-auto">
      {/* Top horizontal line */}
      <hr className="w-full border-t border-gray-300 my-2" />

      {/* Year Timeline Section */}
      <div className="relative w-full">
        <Timeline
          items={years}
          selectedValueKey={`year-${currentYear}`}
          onValueChange={handleYearChange}
          itemWidth={itemWidth}
        />
        {/* Visual indicator for the center of the timeline */}
        <div
          className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-[--item-width] border-x border-primary pointer-events-none"
          style={{ "--item-width": `${itemWidth}px` } as React.CSSProperties}
        />
      </div>

      {/* First separating horizontal line */}
      <hr className="w-full border-t border-gray-300 my-2" />

      {/* Month Timeline Section */}
      <div className="relative w-full flex items-center justify-center">
        <Timeline
          items={months}
          selectedValueKey={selectedMonthUniqueKey}
          onValueChange={handleMonthChange}
          itemWidth={itemWidth}
          className="flex-grow"
        />
        {/* Visual indicator for the center of the timeline */}
        <div
          className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-[--item-width] border-x border-primary pointer-events-none"
          style={{ "--item-width": `${itemWidth}px` } as React.CSSProperties}
        />
      </div>

      {/* Second separating horizontal line */}
      <hr className="w-full border-t border-gray-300 my-2" />

      {/* Day Timeline Section */}
      <div className="relative w-full flex items-center justify-center">
        <Timeline
          items={days}
          selectedValueKey={`day-${currentDay}`}
          onValueChange={handleDayChange}
          itemWidth={itemWidth}
          className="flex-grow"
        />
        {/* Visual indicator for the center of the timeline */}
        <div
          className="absolute inset-y-0 left-1/2 -translate-x-1/2 w-[--item-width] border-x border-primary pointer-events-none"
          style={{ "--item-width": `${itemWidth}px` } as React.CSSProperties}
        />
      </div>

      {/* Bottom horizontal line */}
      <hr className="w-full border-t border-gray-300 my-2" />

      {/* Collapsible Card Section */}
      <div className="relative w-full flex flex-col items-center mt-4">
        <Button
          variant="ghost"
          className="relative -top-4 px-4 py-2 text-sm text-gradient-goldorange hover:text-gradient-goldorange w-full"
          onClick={() => setCollapsed((c) => !c)}
          aria-expanded={!collapsed}
        >
          查看当日烟花表演
        </Button>
        <div
          style={{
            display: collapsed ? 'none' : 'block',
            width: '100vw',
            maxWidth: '100vw',
            left: 0,
            right: 0,
            background: 'transparent',
            border: 'none',
            position: 'relative',
            zIndex: 50,
          }}
          className="mx-0 rounded-none p-0"
        >
          <DateSpecificCards date={date} textClassName="text-gradient-goldorange" />
        </div>
      </div>

      {/* Display selected date */}
      {/* <div className="mt-4 text-lg font-semibold">Selected Date: {format(date, "PPP")}</div> */}
    </div>
  )
} 